aiohappyeyeballs==2.4.3
aiohttp==3.11.8
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.6.2.post1
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==2.4.1
async-lru==2.0.4
attrs==24.2.0
azure-common==1.1.28
azure-core==1.32.0
azure-cosmos==4.7.0
azure-functions==1.21.3
azure-search-documents==11.5.2
azure-storage-blob==12.23.1
babel==2.16.0
beautifulsoup4==4.12.3
bleach==6.2.0
cachetools==5.5.0
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.4.0
click==8.1.7
colorama==0.4.6
comm==0.2.2
contourpy==1.3.1
cryptography==44.0.0
cycler==0.12.1
dataclasses-json==0.6.7
DateTime==5.5
debugpy==1.8.7
decorator==5.1.1
defusedxml==0.7.1
distro==1.9.0
docxcompose==1.4.0
docxtpl==0.18.0
duckduckgo_search==6.3.3
et_xmlfile==2.0.0
executing==2.1.0
fastapi==0.115.12
fastjsonschema==2.20.0
fonttools==4.55.2
fqdn==1.5.1
frozenlist==1.5.0
google-api-core==2.23.0
google-api-python-client==2.154.0
google-auth==2.36.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.1
googleapis-common-protos==1.66.0
greenlet==3.1.1
grpcio==1.68.0
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.0
idna==3.10
ipykernel==6.29.5
ipython==8.29.0
isodate==0.7.2
isoduration==20.11.0
jedi==0.19.1
Jinja2==3.1.4
jiter==0.8.0
json5==0.9.25
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab==4.2.5
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
kiwisolver==1.4.7
langchain==0.3.4
langchain-community==0.3.3
langchain-core==0.3.21
langchain-experimental==0.3.3
langchain-google-community==2.0.1
langchain-openai==0.2.3
langchain-text-splitters==0.3.2
langsmith==0.1.147
lxml==5.3.0
markdown2==2.5.1
MarkupSafe==3.0.2
marshmallow==3.23.1
matplotlib==3.9.3
matplotlib-inline==0.1.7
mistune==3.0.2
multidict==6.1.0
mypy-extensions==1.0.0
narwhals==1.25.2
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
notebook==7.2.2
notebook_shim==0.2.4
numpy==1.26.4
openai==1.55.3
openpyxl==3.1.5
orjson==3.10.12
overrides==7.7.0
packaging==24.2
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
pillow==11.0.0
platformdirs==4.3.6
plotly==6.0.0
primp==0.8.1
prometheus_client==0.21.0
prompt_toolkit==3.0.48
propcache==0.2.0
proto-plus==1.25.0
protobuf==5.29.0
psutil==6.1.0
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.2
pydantic-settings==2.6.1
pydantic_core==2.27.1
Pygments==2.18.0
pyparsing==3.2.0
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.0
python-json-logger==2.0.7
python-pptx==1.0.2
pytz==2024.2
pywin32==308
pywinpty==2.0.14
PyYAML==6.0.2
pyzmq==26.2.0
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rpds-py==0.20.1
rsa==4.9
Send2Trash==1.8.3
six==1.16.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.36
stack-data==0.6.3
starlette==0.46.2
tabulate==0.9.0
tenacity==9.0.0
terminado==0.18.1
tiktoken==0.8.0
tinycss2==1.4.0
tornado==6.4.1
tqdm==4.67.1
traitlets==5.14.3
types-python-dateutil==2.9.0.20241003
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
uri-template==1.3.0
uritemplate==4.1.1
urllib3==2.2.3
uvicorn==0.34.3
wcwidth==0.2.13
webcolors==24.8.0
webencodings==0.5.1
websocket-client==1.8.0
XlsxWriter==3.2.0
yarl==1.18.0
zope.interface==7.2
annotated-types==0.7.0                                                                                                                                                  
anyio==4.9.0
azure-core==1.34.0
azure-cosmos==4.9.0
bcrypt==4.3.0
certifi==2025.4.26
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
dnspython==2.7.0
email_validator==2.2.0
exceptiongroup==1.3.0
fastapi==0.115.12
h11==0.16.0
idna==3.10
passlib==1.7.4
pydantic==2.11.5
pydantic_core==2.33.2
python-dotenv==1.1.0
requests==2.32.4
six==1.17.0
sniffio==1.3.1
starlette==0.46.2
typing-inspection==0.4.1
typing_extensions==4.14.0
urllib3==2.4.0
uvicorn==0.34.3