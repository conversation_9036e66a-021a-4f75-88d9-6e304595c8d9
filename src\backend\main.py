from fastapi import FastAPI
from api.v1.endpoints import organizations, assessments, users, integrations
from core.config import settings

app = FastAPI(title="AI Assessment Platform", version="1.0.0")

# Include API routers
app.include_router(organizations.router, prefix="/api/v1", tags=["organizations"])
app.include_router(assessments.router, prefix="/api/v1", tags=["assessments"])
app.include_router(users.router, prefix="/api/v1", tags=["users"])
app.include_router(integrations.router, prefix="/api/v1", tags=["integrations"])

@app.get("/")
async def root():
    return {"message": "Welcome to the AI Assessment Platform"}