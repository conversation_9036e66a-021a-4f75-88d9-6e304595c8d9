from azure.cosmos import Cosmos<PERSON><PERSON>, Partition<PERSON>ey, exceptions
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

COSMOS_DB_URL = os.getenv("COSMOS_DB_URL")
COSMOS_DB_KEY = os.getenv("COSMOS_DB_KEY")
DATABASE_NAME = "aicruit"  # your database name

# Initialize Cosmos client
client = CosmosClient(COSMOS_DB_URL, credential=COSMOS_DB_KEY)
database = client.create_database_if_not_exists(DATABASE_NAME)

# List of containers to create
containers = [
    {"name": "users", "partition_key": "/id"},
    {"name": "organizations", "partition_key": "/id"},
    {"name": "candidates", "partition_key": "/id"},
    {"name": "candidate_education", "partition_key": "/candidate_id"},
    {"name": "candidate_experience", "partition_key": "/candidate_id"},
    {"name": "industries", "partition_key": "/id"},
    {"name": "skills", "partition_key": "/id"},
    {"name": "question_bank", "partition_key": "/organization_id"},
    {"name": "test_templates", "partition_key": "/organization_id"},
    {"name": "job_postings", "partition_key": "/organization_id"},
    {"name": "job_applications", "partition_key": "/job_posting_id"},
    {"name": "assessment_sessions", "partition_key": "/candidate_id"},
    {"name": "proctoring_sessions", "partition_key": "/assessment_session_id"},
]

# Loop through and create containers
for container in containers:
    try:
        database.create_container_if_not_exists(
            id=container["name"],
            partition_key=PartitionKey(path=container["partition_key"]),
            offer_throughput=400,  # You can adjust throughput here
        )
        print(f"Container '{container['name']}' created or already exists.")
    except exceptions.CosmosHttpResponseError as e:
        print(f"Error creating container {container['name']}: {e}")
